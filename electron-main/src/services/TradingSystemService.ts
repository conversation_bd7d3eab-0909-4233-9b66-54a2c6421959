/**
 * Electron 主进程交易系统服务
 * ============================
 * 管理交易系统的生命周期和与渲染进程的通信
 */

import { EventEmitter } from "events";
import { TradingSystem } from "../trading/TradingSystem";
import { getConfig, validateConfig } from "../utils/configManager";
import { TaskCreateOptions, TaskStatus, Task } from "../trading/task-types";
import { sendToRenderer } from "../ipc/handlers";

export interface TradingSystemServiceConfig {
    environment?: "production" | "test" | "development";
    enableAutoStart?: boolean;
}

/**
 * 交易系统服务状态
 */
export interface TradingSystemStatus {
    initialized: boolean;
    marketConnected: boolean;
    tradingConnected: boolean;
    activeTasksCount: number;
    lastUpdate: number;
    error?: string;
}

/**
 * 交易系统服务
 * 负责管理交易系统实例和处理主进程相关逻辑
 */
export class TradingSystemService extends EventEmitter {
    private tradingSystem: TradingSystem | null = null;
    private isInitialized = false;
    private config: TradingSystemServiceConfig;
    private heartbeatInterval: NodeJS.Timeout | null = null;

    constructor(config: TradingSystemServiceConfig = {}) {
        super();
        this.config = {
            environment: "development",
            enableAutoStart: false,
            ...config
        };

        // 绑定事件处理器
        this.setupEventHandlers();
    }

    /**
     * 初始化交易系统服务
     */
    async initialize(): Promise<void> {
        try {
            console.log(`[TradingSystemService] 开始初始化 (环境: ${this.config.environment})`);

            // 获取环境配置
            const systemConfig = getConfig();

            // 验证配置
            if (!validateConfig(systemConfig)) {
                throw new Error("配置验证失败");
            }

            // 创建交易系统实例
            this.tradingSystem = await TradingSystem.createWithConfig(this.config.environment!);

            // 初始化交易系统
            await this.tradingSystem.initialize();

            this.isInitialized = true;

            // 启动心跳监控
            this.startHeartbeat();

            console.log("[TradingSystemService] 初始化完成");

            // 发送初始化完成事件到渲染进程
            this.broadcastEvent("trading-system:initialized", {
                environment: this.config.environment,
                timestamp: Date.now()
            });

            this.emit("initialized");
        } catch (error) {
            console.error("[TradingSystemService] 初始化失败:", error);
            this.broadcastEvent("trading-system:error", {
                type: "initialization",
                message: error instanceof Error ? error.message : "未知错误",
                timestamp: Date.now()
            });
            throw error;
        }
    }

    /**
     * 关闭交易系统服务
     */
    async shutdown(): Promise<void> {
        try {
            console.log("[TradingSystemService] 开始关闭服务");

            // 停止心跳
            if (this.heartbeatInterval) {
                clearInterval(this.heartbeatInterval);
                this.heartbeatInterval = null;
            }

            // 关闭交易系统
            if (this.tradingSystem) {
                await this.tradingSystem.shutdown();
                this.tradingSystem = null;
            }

            this.isInitialized = false;

            console.log("[TradingSystemService] 服务关闭完成");

            this.broadcastEvent("trading-system:shutdown", {
                timestamp: Date.now()
            });

            this.emit("shutdown");
        } catch (error) {
            console.error("[TradingSystemService] 关闭服务失败:", error);
            throw error;
        }
    }

    /**
     * 获取交易系统状态
     */
    getStatus(): TradingSystemStatus {
        if (!this.tradingSystem) {
            return {
                initialized: false,
                marketConnected: false,
                tradingConnected: false,
                activeTasksCount: 0,
                lastUpdate: Date.now(),
                error: "交易系统未初始化"
            };
        }

        try {
            // 分别获取各个组件状态，避免一个组件失败影响整体
            let marketConnected = false;
            let tradingConnected = false;
            let activeTasksCount = 0;

            // 安全获取行情连接状态
            try {
                const marketStatus = this.tradingSystem.getMarketDataStats();
                marketConnected = marketStatus ? marketStatus.isConnected : false;
            } catch (error) {
                console.warn("[TradingSystemService] 获取行情状态失败:", error);
                marketConnected = false;
            }

            // 安全获取交易连接状态
            try {
                const tradingStatus = this.tradingSystem.getTradingStatus();
                tradingConnected = tradingStatus && tradingStatus.huasheng ? tradingStatus.huasheng.connected : false;
            } catch (error) {
                console.warn("[TradingSystemService] 获取交易状态失败:", error);
                tradingConnected = false;
            }

            // 安全获取任务统计
            try {
                const taskStats = this.tradingSystem.getStatistics();
                activeTasksCount = taskStats.runningTasks;
            } catch (error) {
                console.warn("[TradingSystemService] 获取任务统计失败:", error);
                activeTasksCount = 0;
            }

            return {
                initialized: this.isInitialized,
                marketConnected,
                tradingConnected,
                activeTasksCount,
                lastUpdate: Date.now()
            };
        } catch (error) {
            console.error("[TradingSystemService] 获取系统状态失败:", error);
            return {
                initialized: this.isInitialized,
                marketConnected: false,
                tradingConnected: false,
                activeTasksCount: 0,
                lastUpdate: Date.now(),
                error: error instanceof Error ? error.message : "获取状态失败"
            };
        }
    }

    /**
     * 创建交易任务
     */
    async createTask(taskConfig: TaskCreateOptions): Promise<string> {
        if (!this.tradingSystem) {
            throw new Error("交易系统未初始化");
        }

        try {
            const taskId = await this.tradingSystem.createTask(taskConfig);

            this.broadcastEvent("trading-system:task-created", {
                taskId,
                taskConfig,
                timestamp: Date.now()
            });

            return taskId;
        } catch (error) {
            console.error("[TradingSystemService] 创建任务失败:", error);
            throw error;
        }
    }

    /**
     * 启动任务
     */
    async startTask(taskId: string): Promise<void> {
        if (!this.tradingSystem) {
            throw new Error("交易系统未初始化");
        }

        try {
            await this.tradingSystem.startTask(taskId);

            this.broadcastEvent("trading-system:task-started", {
                taskId,
                timestamp: Date.now()
            });
        } catch (error) {
            console.error("[TradingSystemService] 启动任务失败:", error);
            throw error;
        }
    }

    /**
     * 停止任务
     */
    async stopTask(taskId: string): Promise<void> {
        if (!this.tradingSystem) {
            throw new Error("交易系统未初始化");
        }

        try {
            await this.tradingSystem.stopTask(taskId);

            this.broadcastEvent("trading-system:task-stopped", {
                taskId,
                timestamp: Date.now()
            });
        } catch (error) {
            console.error("[TradingSystemService] 停止任务失败:", error);
            throw error;
        }
    }

    /**
     * 获取任务列表
     */
    getTaskList(): Task[] {
        if (!this.tradingSystem) {
            return [];
        }

        try {
            return this.tradingSystem.getAllTasks();
        } catch (error) {
            console.error("[TradingSystemService] 获取任务列表失败:", error);
            return [];
        }
    }

    /**
     * 获取任务状态
     */
    getTaskStatus(taskId: string): TaskStatus | null {
        if (!this.tradingSystem) {
            return null;
        }

        try {
            const task = this.tradingSystem.getTask(taskId);
            return task ? task.status : null;
        } catch (error) {
            console.error("[TradingSystemService] 获取任务状态失败:", error);
            return null;
        }
    }

    /**
     * 设置事件处理器
     */
    private setupEventHandlers(): void {
        // 监听交易系统事件并转发到渲染进程
        this.on("trading-system-event", (eventType: string, data: any) => {
            this.broadcastEvent(`trading-system:${eventType}`, data);
        });
    }

    /**
     * 启动心跳监控
     */
    private startHeartbeat(): void {
        this.heartbeatInterval = setInterval(() => {
            if (this.isInitialized && this.tradingSystem) {
                const status = this.getStatus();
                this.broadcastEvent("trading-system:heartbeat", status);
            }
        }, 5000); // 每5秒发送一次心跳
    }

    /**
     * 广播事件到渲染进程
     */
    private broadcastEvent(eventType: string, data: any): void {
        try {
            sendToRenderer(eventType, data);
        } catch (error) {
            console.error("[TradingSystemService] 广播事件失败:", eventType, error);
        }
    }

    /**
     * 获取交易系统实例（仅供内部使用）
     */
    getTradingSystem(): TradingSystem | null {
        return this.tradingSystem;
    }

    /**
     * 检查是否已初始化
     */
    isReady(): boolean {
        return this.isInitialized && this.tradingSystem !== null;
    }
}

// 导出全局实例
export let tradingSystemService: TradingSystemService | null = null;

/**
 * 初始化全局交易系统服务
 */
export function initializeTradingSystemService(config?: TradingSystemServiceConfig): TradingSystemService {
    if (tradingSystemService) {
        console.warn("[TradingSystemService] 服务已存在，返回现有实例");
        return tradingSystemService;
    }

    tradingSystemService = new TradingSystemService(config);
    return tradingSystemService;
}

/**
 * 获取全局交易系统服务
 */
export function getTradingSystemService(): TradingSystemService | null {
    return tradingSystemService;
}
