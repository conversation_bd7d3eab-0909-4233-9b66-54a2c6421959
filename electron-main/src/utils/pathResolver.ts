/**
 * 路径解析工具
 * =============
 * 提供在开发环境和打包环境中都能正确工作的路径解析功能
 */

import path from "path";
import fs from "fs";

/**
 * 路径解析器类
 */
export class PathResolver {
    private static instance: PathResolver;
    private isPackaged: boolean;
    private appRoot: string;
    private electronMainSrc: string;

    private constructor() {
        this.isPackaged = this.detectPackagedEnvironment();
        this.appRoot = this.resolveAppRoot();
        this.electronMainSrc = this.resolveElectronMainSrc();

        // 调试信息
        if (process.env.DEBUG_PATH_RESOLVER) {
            console.log("[PathResolver] 初始化:");
            console.log(`  - isPackaged: ${this.isPackaged}`);
            console.log(`  - appRoot: ${this.appRoot}`);
            console.log(`  - electronMainSrc: ${this.electronMainSrc}`);
            console.log(`  - __dirname: ${__dirname}`);
        }
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): PathResolver {
        if (!PathResolver.instance) {
            PathResolver.instance = new PathResolver();
        }
        return PathResolver.instance;
    }

    /**
     * 检测是否为打包环境
     */
    private detectPackagedEnvironment(): boolean {
        // 主要检测方法：检查是否在 asar 文件中
        const asarCheck = __dirname.includes(".asar");

        // 辅助检测：检查项目结构
        const projectStructureCheck = () => {
            try {
                // 在开发环境中，应该能找到项目根目录的 tsconfig.json
                const projectRoot = path.resolve(__dirname, "../../..");
                return !fs.existsSync(path.join(projectRoot, "tsconfig.json"));
            } catch {
                return false;
            }
        };

        // 环境变量检测（仅作为辅助）
        const envCheck = process.env.NODE_ENV === "production";

        const isPackaged = asarCheck || (envCheck && projectStructureCheck());

        if (process.env.DEBUG_PATH_RESOLVER) {
            console.log("[PathResolver] 打包环境检测:");
            console.log(`  - __dirname: ${__dirname}`);
            console.log(`  - asar检测: ${asarCheck}`);
            console.log(`  - 环境变量检测: ${envCheck}`);
            console.log(`  - 项目结构检测: ${projectStructureCheck()}`);
            console.log(`  - 最终结果: ${isPackaged}`);
        }

        return isPackaged;
    }

    /**
     * 解析应用根目录
     */
    private resolveAppRoot(): string {
        if (this.isPackaged) {
            // 打包环境：从 __dirname 推断
            const asarMatch = __dirname.match(/^(.+\.asar)/);
            if (asarMatch) {
                return asarMatch[1];
            }
            // 备用方案
            return path.resolve(__dirname, "../../..");
        } else {
            // 开发环境：项目根目录
            return path.resolve(__dirname, "../../..");
        }
    }

    /**
     * 解析 Electron 主进程源码目录
     */
    private resolveElectronMainSrc(): string {
        if (this.isPackaged) {
            // 打包环境：asar 文件中的路径
            return path.join(this.appRoot, "electron-main/dist/src");
        } else {
            // 开发环境：编译后的 dist 目录
            return path.resolve(__dirname, "..");
        }
    }

    /**
     * 解析路径别名
     */
    public resolveAlias(aliasPath: string): string | null {
        if (!aliasPath.startsWith("@/")) {
            return null;
        }

        const relativePath = aliasPath.substring(2);
        const resolvedPath = path.join(this.electronMainSrc, relativePath);

        // 尝试多种文件扩展名
        const candidates = [resolvedPath + ".js", resolvedPath, path.join(resolvedPath, "index.js"), resolvedPath + "/index.js"];

        for (const candidate of candidates) {
            try {
                if (fs.existsSync(candidate) || this.isPackaged) {
                    // 在打包环境中，文件可能在 asar 中，fs.existsSync 可能不准确
                    // 所以我们返回候选路径，让 require 系统处理
                    return candidate;
                }
            } catch {
                // 继续尝试下一个候选路径
            }
        }

        if (process.env.DEBUG_PATH_RESOLVER) {
            console.log(`[PathResolver] 无法解析别名: ${aliasPath}`);
            console.log(`  - 尝试的路径: ${candidates.join(", ")}`);
        }

        return null;
    }

    /**
     * 获取调试信息
     */
    public getDebugInfo(): object {
        return {
            isPackaged: this.isPackaged,
            appRoot: this.appRoot,
            electronMainSrc: this.electronMainSrc,
            __dirname,
            cwd: process.cwd(),
            nodeEnv: process.env.NODE_ENV
        };
    }
}

/**
 * 导出单例实例
 */
export const pathResolver = PathResolver.getInstance();
