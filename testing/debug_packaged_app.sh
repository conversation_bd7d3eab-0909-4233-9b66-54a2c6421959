#!/bin/bash

# 调试打包后的 Electron 应用
# ============================

echo "🔍 调试打包后的 Electron 应用路径解析"
echo "=" * 50

APP_PATH="dist/mac/量化交易终端.app"

if [ ! -d "$APP_PATH" ]; then
    echo "❌ 打包后的应用不存在，请先运行 yarn electron:build"
    exit 1
fi

echo "✅ 找到应用: $APP_PATH"

# 启动应用并启用调试
echo ""
echo "🚀 启动应用（启用路径解析调试）..."
echo "📋 请观察控制台输出中的路径解析信息"
echo ""

# 设置调试环境变量并启动应用
DEBUG_PATH_ALIAS=1 open "$APP_PATH" --args --enable-logging --log-level=0

echo "✅ 应用已启动"
echo ""
echo "📋 请检查以下项目:"
echo "1. 应用是否正常启动"
echo "2. Web 界面是否正常显示"
echo "3. 首页行情状态是否为绿色（已连接）"
echo "4. 控制台是否有路径解析错误"
echo ""
echo "💡 如果行情显示为绿色连接状态，说明路径别名修复成功！"
echo "💡 如果仍有问题，请查看控制台的调试输出"
echo ""
echo "⏳ 等待 10 秒后自动检查应用状态..."

sleep 10

# 检查应用是否在运行
if pgrep -f "量化交易终端" > /dev/null; then
    echo "✅ 应用正在运行"
    
    # 提示用户手动检查
    echo ""
    read -p "应用是否正常工作？行情是否显示为绿色连接状态？(y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🎉 太好了！路径别名修复成功！"
    else
        echo "❌ 仍有问题，让我们查看更多调试信息..."
        echo ""
        echo "📋 请检查以下可能的问题："
        echo "1. 控制台是否有 [PathAlias] 错误信息"
        echo "2. 是否有模块加载失败的错误"
        echo "3. 交易系统服务是否正常初始化"
    fi
    
    # 询问是否关闭应用
    echo ""
    read -p "是否关闭应用？(y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🛑 关闭应用..."
        pkill -f "量化交易终端"
        echo "✅ 应用已关闭"
    else
        echo "📱 应用继续运行，您可以手动关闭"
    fi
else
    echo "❌ 应用未在运行或启动失败"
    echo "📋 可能的问题："
    echo "1. 路径解析仍有问题"
    echo "2. 依赖模块加载失败"
    echo "3. 应用启动时崩溃"
fi

echo ""
echo "🔧 调试完成"
