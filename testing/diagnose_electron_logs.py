#!/usr/bin/env python3
"""
诊断 Electron 应用日志
====================
查看 Electron 应用的控制台输出和错误日志
"""

import subprocess
import sys
import time
import os
from pathlib import Path
import signal

def start_app_with_logging():
    """启动应用并捕获日志"""
    print("🚀 启动应用并捕获日志...")
    
    app_path = Path("dist/mac/量化交易终端.app")
    if not app_path.exists():
        print("❌ 应用不存在")
        return None
    
    # 启动应用并捕获输出
    try:
        # 使用 open 命令启动应用，并通过环境变量启用详细日志
        env = os.environ.copy()
        env['ELECTRON_ENABLE_LOGGING'] = '1'
        env['DEBUG'] = '*'
        
        # 启动应用
        process = subprocess.Popen([
            "open", "-a", str(app_path),
            "--args", 
            "--enable-logging",
            "--log-level=0",
            "--remote-debugging-port=9222"
        ], env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("✅ 应用已启动")
        return process
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def check_console_logs():
    """检查系统控制台日志"""
    print("\n📋 检查系统控制台日志...")
    
    try:
        # 查看最近的系统日志，过滤 Electron 相关
        result = subprocess.run([
            "log", "show", "--last", "2m", "--predicate", 
            'process == "量化交易终端" OR process CONTAINS "Electron"',
            "--style", "syslog"
        ], capture_output=True, text=True, timeout=10)
        
        if result.stdout:
            print("📄 系统日志:")
            print(result.stdout)
        else:
            print("❌ 没有找到相关系统日志")
            
    except subprocess.TimeoutExpired:
        print("⏰ 日志查询超时")
    except Exception as e:
        print(f"❌ 查询日志失败: {e}")

def check_crash_reports():
    """检查崩溃报告"""
    print("\n💥 检查崩溃报告...")
    
    crash_dirs = [
        Path.home() / "Library/Logs/DiagnosticReports",
        "/Library/Logs/DiagnosticReports"
    ]
    
    for crash_dir in crash_dirs:
        if crash_dir.exists():
            # 查找最近的崩溃报告
            crash_files = list(crash_dir.glob("*量化交易终端*"))
            crash_files.extend(list(crash_dir.glob("*Electron*")))
            
            # 按修改时间排序，获取最新的
            recent_crashes = sorted(crash_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
            
            if recent_crashes:
                print(f"📄 在 {crash_dir} 找到崩溃报告:")
                for crash_file in recent_crashes:
                    mtime = time.ctime(crash_file.stat().st_mtime)
                    print(f"  - {crash_file.name} ({mtime})")
                    
                    # 显示最新崩溃报告的前几行
                    if crash_file == recent_crashes[0]:
                        try:
                            with open(crash_file, 'r') as f:
                                lines = f.readlines()[:20]
                                print("    前20行内容:")
                                for line in lines:
                                    print(f"    {line.rstrip()}")
                        except Exception as e:
                            print(f"    读取失败: {e}")
            else:
                print(f"❌ 在 {crash_dir} 没有找到相关崩溃报告")

def main():
    """主函数"""
    print("🔍 Electron 应用日志诊断工具")
    print("=" * 40)
    
    # 启动应用
    process = start_app_with_logging()
    if not process:
        return
    
    print("⏳ 等待 8 秒让应用启动...")
    time.sleep(8)
    
    # 检查应用是否还在运行
    if process.poll() is None:
        print("✅ 应用正在运行")
        
        # 检查进程是否真的在运行
        try:
            pgrep_result = subprocess.run(["pgrep", "-f", "量化交易终端"], 
                                        capture_output=True, text=True)
            if pgrep_result.returncode == 0:
                print(f"✅ 找到进程 PID: {pgrep_result.stdout.strip()}")
            else:
                print("❌ 没有找到运行中的进程")
        except Exception as e:
            print(f"❌ 检查进程失败: {e}")
            
    else:
        print("❌ 应用已退出")
        stdout, stderr = process.communicate()
        if stdout:
            print(f"标准输出:\n{stdout}")
        if stderr:
            print(f"错误输出:\n{stderr}")
    
    # 检查各种日志
    check_console_logs()
    check_crash_reports()
    
    # 清理
    try:
        subprocess.run(["pkill", "-f", "量化交易终端"], capture_output=True)
        print("\n🛑 已清理应用进程")
    except:
        pass
    
    print("\n🔧 诊断完成")
    print("\n💡 建议:")
    print("1. 检查上述日志中是否有模块加载错误")
    print("2. 查看是否有路径解析相关的错误")
    print("3. 确认交易系统服务是否正常初始化")

if __name__ == "__main__":
    main()
