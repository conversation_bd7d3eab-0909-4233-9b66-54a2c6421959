#!/usr/bin/env python3
"""
测试打包后的 Electron 应用路径解析
=====================================
用于验证路径别名在打包环境中是否正常工作
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def test_packaged_app():
    """测试打包后的应用"""
    print("🧪 测试打包后的 Electron 应用路径解析")
    print("=" * 50)

    # 应用路径
    app_path = Path("dist/mac/量化交易终端.app")

    if not app_path.exists():
        print("❌ 打包后的应用不存在，请先运行 yarn electron:build")
        return False

    print(f"✅ 找到应用: {app_path}")

    # 启动应用并捕获日志
    print("\n🚀 启动应用...")
    try:
        # 直接启动应用
        subprocess.run(["open", str(app_path)], check=True)

        print("✅ 应用已启动")
        print("⏳ 等待 8 秒让应用完全加载...")
        time.sleep(8)

        # 检查应用是否在运行
        result = subprocess.run([
            "pgrep", "-f", "量化交易终端"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 应用正在运行")

            # 提示用户检查应用状态
            print("\n📋 请检查以下项目:")
            print("1. 应用是否正常启动")
            print("2. Web 界面是否正常显示")
            print("3. 首页行情状态是否为绿色（已连接）")
            print("4. 控制台是否有路径解析错误")
            print("\n💡 如果行情显示为绿色连接状态，说明路径别名修复成功！")

            # 等待用户确认
            user_input = input("\n应用是否正常工作？(y/n): ").lower().strip()

            # 终止应用
            print("🛑 终止应用...")
            subprocess.run(["pkill", "-f", "量化交易终端"], capture_output=True)

            return user_input == 'y'
        else:
            print("❌ 应用未在运行或启动失败")
            return False

    except Exception as e:
        print(f"❌ 启动应用时出错: {e}")
        return False

def check_console_logs():
    """检查控制台日志"""
    print("\n📋 检查控制台日志")
    print("-" * 30)
    
    # macOS 控制台日志路径
    log_paths = [
        "~/Library/Logs/量化交易终端/main.log",
        "/var/log/system.log"
    ]
    
    for log_path in log_paths:
        expanded_path = Path(log_path).expanduser()
        if expanded_path.exists():
            print(f"✅ 找到日志文件: {expanded_path}")
            try:
                # 读取最后几行日志
                result = subprocess.run([
                    "tail", "-20", str(expanded_path)
                ], capture_output=True, text=True)
                
                if result.stdout:
                    print("最近的日志:")
                    print(result.stdout)
                    
            except Exception as e:
                print(f"读取日志失败: {e}")
        else:
            print(f"❌ 日志文件不存在: {expanded_path}")

def main():
    """主函数"""
    print("🔧 Electron 路径别名修复验证工具")
    print("=" * 40)
    
    # 检查当前目录
    if not Path("package.json").exists():
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 测试打包后的应用
    success = test_packaged_app()
    
    # 检查日志
    check_console_logs()
    
    if success:
        print("\n✅ 测试完成")
        print("\n📝 如果应用运行正常，说明路径别名修复成功")
        print("📝 如果仍有问题，请检查控制台错误信息")
    else:
        print("\n❌ 测试失败")
        print("📝 请检查构建过程和错误日志")

if __name__ == "__main__":
    main()
